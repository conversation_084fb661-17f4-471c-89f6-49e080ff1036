# 🕷️ Puppeteer 可视化网页抓取平台

基于 Vue3 + TypeScript + Element-plus + Node.js + Puppeteer 构建的可视化网页抓取平台，支持通过图形界面选择网页元素、配置抓取规则，并一键运行爬虫任务导出数据。

## ✨ 功能特性

### 核心功能

- **🎯 可视化元素选择** - 在网页中直接点击要抓取的内容
- **📝 抓取规则配置** - 支持文本、链接、图片、属性等多种数据类型
- **🚀 一键执行抓取** - 自动化网页访问和数据提取
- **📊 数据结果展示** - 表格形式展示抓取数据，支持搜索筛选
- **💾 数据导出** - 支持导出为 CSV 和 JSON 格式

### 技术特色

- **现代化前端** - Vue3 Composition API + TypeScript
- **优雅UI设计** - Element Plus 组件库，响应式布局
- **强大后端** - Node.js + Express + Puppeteer
- **实时状态更新** - 任务执行状态实时反馈
- **分页抓取** - 支持多页面自动翻页抓取

## 🏗️ 技术架构

### 前端技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集
- **Element Plus** - Vue 3组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Vite** - 现代化构建工具
- **Axios** - HTTP客户端

### 后端技术栈

- **Node.js 20+** - JavaScript运行时
- **Express** - Web应用框架
- **Puppeteer** - 无头浏览器控制
- **TypeScript** - 类型安全开发
- **JSON文件存储** - 简化数据持久化

## 📋 系统要求

- **Node.js** >= 20.0.0
- **pnpm** >= 8.0.0
- **Chrome/Chromium** (Puppeteer自动下载)

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <project-url>
cd puppeteer-web-scraper
```

### 2. 安装依赖

```bash
# 安装根目录和所有工作区依赖
pnpm run install:all

# 或者分别安装
pnpm install
cd frontend && pnpm install
cd ../backend && pnpm install
```

### 3. 启动开发服务

```bash
# 同时启动前端和后端服务
pnpm run dev

# 或者分别启动
pnpm run dev:frontend  # 前端: http://localhost:3000
pnpm run dev:backend   # 后端: http://localhost:3001
```

### 4. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 📁 项目结构

```
puppeteer-web-scraper/
├── backend/                 # 后端代码
│   ├── src/
│   │   ├── types/          # 类型定义
│   │   ├── services/       # 服务层
│   │   │   ├── storage.ts  # 数据存储服务
│   │   │   └── scraper.ts  # Puppeteer抓取服务
│   │   ├── routes/         # API路由
│   │   │   ├── tasks.ts    # 任务管理API
│   │   │   └── scraper.ts  # 抓取相关API
│   │   └── index.ts        # 服务器入口
│   ├── data/               # 数据文件存储目录
│   ├── package.json
│   └── tsconfig.json
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   │   ├── TaskList.vue     # 任务列表页
│   │   │   ├── TaskConfig.vue   # 任务配置页
│   │   │   └── TaskResult.vue   # 结果展示页
│   │   ├── api/            # API接口封装
│   │   ├── types/          # 类型定义
│   │   ├── router/         # 路由配置
│   │   ├── App.vue         # 根组件
│   │   └── main.ts         # 应用入口
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
├── package.json            # 根配置文件
├── prd.md                  # 产品需求文档
└── README.md               # 项目说明文档
```

## 🎮 使用指南

### 1. 创建抓取任务

1. 点击 "新建任务" 按钮
2. 输入任务名称和目标网址
3. 点击 "加载页面" 预览网页

### 2. 配置抓取字段

1. 点击 "开启选择模式"
2. 在预览页面中点击要抓取的元素
3. 系统自动生成CSS选择器
4. 配置字段名称和提取类型
5. 可添加多个字段和分页选择器

### 3. 执行抓取任务

1. 保存任务配置
2. 在任务列表中点击 "执行抓取"
3. 系统实时显示执行状态
4. 完成后可查看抓取结果

### 4. 导出数据

1. 在结果页面查看抓取的数据
2. 支持搜索和列筛选
3. 选择导出格式 (CSV/JSON)
4. 下载导出文件

## 🔧 配置说明

### 环境变量

后端支持以下环境变量：

```bash
PORT=3001                    # 服务器端口
NODE_ENV=development         # 环境模式
```

### Puppeteer 配置

抓取服务默认配置：

- 无头模式运行
- 禁用沙箱（适配Docker）
- 30秒超时设置
- 最多抓取10页数据

### 数据存储

- 任务数据：`backend/data/tasks.json`
- 抓取结果：`backend/data/results.json`
- 自动创建数据目录

## 🏭 生产部署

### 1. 构建项目

```bash
pnpm run build
```

### 2. 启动生产服务

```bash
cd backend
pnpm start
```

### 3. 环境配置

```bash
export NODE_ENV=production
export PORT=3001
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📝 开发说明

### 代码规范

- 使用 TypeScript 进行类型安全开发
- 遵循 ESLint 和 Prettier 规范
- 组件采用 Vue 3 Composition API
- API 采用 RESTful 设计

### 调试技巧

1. 查看浏览器控制台错误信息
2. 检查后端服务日志
3. 验证CSS选择器是否正确
4. 测试目标网站的访问权限

## ⚠️ 注意事项

1. **网站访问限制** - 某些网站可能有反爬虫机制
2. **选择器失效** - 网站结构变化可能导致选择器失效
3. **性能考虑** - 大量数据抓取时注意性能和内存使用
4. **法律合规** - 遵守目标网站的robots.txt和服务条款

---

**开始你的网页抓取之旅吧！** 🎉
