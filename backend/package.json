{"name": "puppeteer-web-scraper-backend", "version": "1.0.0", "description": "Puppeteer可视化抓取平台后端API服务", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "puppeteer": "^24.15.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0"}}