import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import path from 'path';

import tasksRouter from './routes/tasks';
import scraperRouter from './routes/scraper';

const app = express();
const PORT = process.env.PORT || 3001;

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false, // 为了支持iframe
}));

// CORS配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['http://localhost:3000'] // 生产环境前端域名
    : true, // 开发环境允许所有来源
  credentials: true
}));

// 通用限速中间件 - 较为宽松
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 每个IP最多200次请求
  message: {
    success: false,
    error: '请求过于频繁，请稍后再试'
  }
});

// 预览接口专用限速 - 更宽松
const previewLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 20, // 每分钟最多20次预览请求
  message: {
    success: false,
    error: '页面预览请求过于频繁，请稍后再试'
  }
});

// 抓取任务限速 - 较严格
const taskLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 10, // 每5分钟最多10次任务执行
  message: {
    success: false,
    error: '任务执行过于频繁，请稍后再试'
  }
});

// 应用限速中间件
app.use('/api', generalLimiter);
app.use('/api/scraper/preview', previewLimiter);
app.use('/api/tasks/:id/run', taskLimiter);

// 日志中间件
app.use(morgan('combined'));

// 解析JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API路由
app.use('/api/tasks', tasksRouter);
app.use('/api/scraper', scraperRouter);

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Puppeteer Web Scraper API is running',
    timestamp: new Date().toISOString()
  });
});

// 静态文件服务（生产环境）
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../../frontend/dist')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../../frontend/dist/index.html'));
  });
}

// 错误处理中间件
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('服务器错误:', err);
  
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? '服务器内部错误' 
      : err.message
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
  console.log(`📚 API文档: http://localhost:${PORT}/api/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
}); 