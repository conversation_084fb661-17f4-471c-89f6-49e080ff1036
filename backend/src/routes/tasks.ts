import express from 'express';
import { v4 as uuidv4 } from 'uuid';
import { ScrapingTask, CreateTaskRequest, UpdateTaskRequest, ApiResponse } from '../types';
import { StorageService } from '../services/storage';
import { ScraperService } from '../services/scraper';

const router = express.Router();
const storage = StorageService.getInstance();
const scraper = ScraperService.getInstance();

// 获取所有任务
router.get('/', async (req, res) => {
  try {
    const tasks = await storage.readTasks();
    const response: ApiResponse<ScrapingTask[]> = {
      success: true,
      data: tasks
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 获取单个任务
router.get('/:id', async (req, res) => {
  try {
    const task = await storage.getTask(req.params.id);
    if (!task) {
      const response: ApiResponse = {
        success: false,
        error: '任务不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse<ScrapingTask> = {
      success: true,
      data: task
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 创建新任务
router.post('/', async (req, res) => {
  try {
    const taskData: CreateTaskRequest = req.body;
    
    // 验证必需字段
    if (!taskData.name || !taskData.url || !taskData.fields || taskData.fields.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: '缺少必需字段'
      };
      return res.status(400).json(response);
    }

    const newTask: ScrapingTask = {
      id: uuidv4(),
      name: taskData.name,
      url: taskData.url,
      fields: taskData.fields,
      nextPageSelector: taskData.nextPageSelector,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const createdTask = await storage.createTask(newTask);
    
    const response: ApiResponse<ScrapingTask> = {
      success: true,
      data: createdTask,
      message: '任务创建成功'
    };
    res.status(201).json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 更新任务
router.put('/:id', async (req, res) => {
  try {
    const updates: UpdateTaskRequest = req.body;
    const updatedTask = await storage.updateTask(req.params.id, updates);
    
    if (!updatedTask) {
      const response: ApiResponse = {
        success: false,
        error: '任务不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse<ScrapingTask> = {
      success: true,
      data: updatedTask,
      message: '任务更新成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 删除任务
router.delete('/:id', async (req, res) => {
  try {
    const deleted = await storage.deleteTask(req.params.id);
    
    if (!deleted) {
      const response: ApiResponse = {
        success: false,
        error: '任务不存在'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      message: '任务删除成功'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 执行抓取任务
router.post('/:id/run', async (req, res) => {
  try {
    const task = await storage.getTask(req.params.id);
    if (!task) {
      const response: ApiResponse = {
        success: false,
        error: '任务不存在'
      };
      return res.status(404).json(response);
    }

    if (task.status === 'running') {
      const response: ApiResponse = {
        success: false,
        error: '任务正在运行中'
      };
      return res.status(400).json(response);
    }

    // 异步执行抓取任务
    scraper.scrapeTask(req.params.id).catch(error => {
      console.error('抓取任务执行失败:', error);
    });

    const response: ApiResponse = {
      success: true,
      message: '抓取任务已开始执行'
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 获取任务执行结果
router.get('/:id/result', async (req, res) => {
  try {
    const result = await storage.getResult(req.params.id);

    if (!result) {
      const response: ApiResponse = {
        success: false,
        error: '暂无抓取结果'
      };
      return res.status(404).json(response);
    }

    const response: ApiResponse = {
      success: true,
      data: result
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 导出任务结果
router.get('/:id/export', async (req, res) => {
  try {
    const { format = 'csv' } = req.query;
    const result = await storage.getResult(req.params.id);

    if (!result) {
      const response: ApiResponse = {
        success: false,
        error: '暂无抓取结果'
      };
      return res.status(404).json(response);
    }

    if (format === 'csv') {
      // 生成CSV格式
      if (result.data.length === 0) {
        return res.status(400).json({
          success: false,
          error: '没有数据可导出'
        });
      }

      const headers = Object.keys(result.data[0]);
      const csvContent = [
        headers.join(','),
        ...result.data.map(row =>
          headers.map(header => {
            const value = row[header] || '';
            // 处理包含逗号或引号的值
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      res.setHeader('Content-Type', 'text/csv; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="task-${req.params.id}-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send('\uFEFF' + csvContent); // 添加BOM以支持中文
    } else if (format === 'json') {
      // 生成JSON格式
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="task-${req.params.id}-${new Date().toISOString().split('T')[0]}.json"`);
      res.json({
        taskId: result.taskId,
        exportedAt: new Date().toISOString(),
        totalCount: result.totalCount,
        data: result.data
      });
    } else {
      const response: ApiResponse = {
        success: false,
        error: '不支持的导出格式'
      };
      return res.status(400).json(response);
    }
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

export default router;