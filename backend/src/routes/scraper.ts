import express from 'express';
import { ApiResponse } from '../types';
import { ScraperService } from '../services/scraper';

const router = express.Router();
const scraper = ScraperService.getInstance();

// 增强的页面预览（支持登录和全屏）
router.post('/preview', async (req, res) => {
  try {
    const { url, fullPage = true, loginConfig } = req.body;
    
    if (!url) {
      const response: ApiResponse = {
        success: false,
        error: '缺少URL参数'
      };
      return res.status(400).json(response);
    }

    const preview = await scraper.getPagePreview(url, fullPage, loginConfig);
    
    const response: ApiResponse = {
      success: true,
      data: preview
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 智能检测页面结构
router.post('/smart-detect', async (req, res) => {
  try {
    const { url, loginConfig } = req.body;
    
    if (!url) {
      const response: ApiResponse = {
        success: false,
        error: '缺少URL参数'
      };
      return res.status(400).json(response);
    }

    const detection = await scraper.smartDetectPageStructure(url, loginConfig);
    
    const response: ApiResponse = {
      success: true,
      data: detection
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 根据坐标获取元素信息
router.post('/element-at-coordinate', async (req, res) => {
  try {
    const { url, x, y, loginConfig } = req.body;
    
    if (!url || x === undefined || y === undefined) {
      const response: ApiResponse = {
        success: false,
        error: '缺少URL或坐标参数'
      };
      return res.status(400).json(response);
    }

    const element = await scraper.getElementAtCoordinate(url, x, y, loginConfig);
    
    const response: ApiResponse = {
      success: true,
      data: element
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 执行页面交互
router.post('/interact', async (req, res) => {
  try {
    const { url, interactions, loginConfig } = req.body;
    
    if (!url || !interactions) {
      const response: ApiResponse = {
        success: false,
        error: '缺少URL或交互参数'
      };
      return res.status(400).json(response);
    }

    const screenshot = await scraper.performPageInteraction(url, interactions, loginConfig);
    
    const response: ApiResponse = {
      success: true,
      data: screenshot
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 验证选择器（保持向后兼容）
router.post('/validate-selector', async (req, res) => {
  try {
    const { url, selector } = req.body;
    
    if (!url || !selector) {
      const response: ApiResponse = {
        success: false,
        error: '缺少URL或选择器参数'
      };
      return res.status(400).json(response);
    }

    const validation = await scraper.validateSelector(url, selector);
    
    const response: ApiResponse = {
      success: true,
      data: validation
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

// 注入选择器脚本（保持向后兼容）
router.post('/inject-selector', async (req, res) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      const response: ApiResponse = {
        success: false,
        error: '缺少URL参数'
      };
      return res.status(400).json(response);
    }

    const html = await scraper.injectSelectorScript(url);
    
    const response: ApiResponse = {
      success: true,
      data: { html }
    };
    res.json(response);
  } catch (error) {
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    res.status(500).json(response);
  }
});

export default router; 