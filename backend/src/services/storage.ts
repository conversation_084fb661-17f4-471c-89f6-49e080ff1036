import fs from 'fs/promises';
import path from 'path';
import { ScrapingTask, ScrapingResult } from '../types';

const DATA_DIR = path.join(__dirname, '../../data');
const TASKS_FILE = path.join(DATA_DIR, 'tasks.json');
const RESULTS_FILE = path.join(DATA_DIR, 'results.json');

export class StorageService {
  private static instance: StorageService;

  static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  async ensureDataDir(): Promise<void> {
    try {
      await fs.access(DATA_DIR);
    } catch {
      await fs.mkdir(DATA_DIR, { recursive: true });
    }
  }

  async ensureFile(filePath: string, defaultData: any = []): Promise<void> {
    try {
      await fs.access(filePath);
    } catch {
      await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
    }
  }

  async readTasks(): Promise<ScrapingTask[]> {
    await this.ensureDataDir();
    await this.ensureFile(TASKS_FILE, []);
    
    try {
      const data = await fs.readFile(TASKS_FILE, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.error('读取任务文件失败:', error);
      return [];
    }
  }

  async saveTasks(tasks: ScrapingTask[]): Promise<void> {
    await this.ensureDataDir();
    await fs.writeFile(TASKS_FILE, JSON.stringify(tasks, null, 2));
  }

  async readResults(): Promise<ScrapingResult[]> {
    await this.ensureDataDir();
    await this.ensureFile(RESULTS_FILE, []);
    
    try {
      const data = await fs.readFile(RESULTS_FILE, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.error('读取结果文件失败:', error);
      return [];
    }
  }

  async saveResults(results: ScrapingResult[]): Promise<void> {
    await this.ensureDataDir();
    await fs.writeFile(RESULTS_FILE, JSON.stringify(results, null, 2));
  }

  async getTask(id: string): Promise<ScrapingTask | null> {
    const tasks = await this.readTasks();
    return tasks.find(task => task.id === id) || null;
  }

  async createTask(task: ScrapingTask): Promise<ScrapingTask> {
    const tasks = await this.readTasks();
    tasks.push(task);
    await this.saveTasks(tasks);
    return task;
  }

  async updateTask(id: string, updates: Partial<ScrapingTask>): Promise<ScrapingTask | null> {
    const tasks = await this.readTasks();
    const index = tasks.findIndex(task => task.id === id);
    
    if (index === -1) {
      return null;
    }

    tasks[index] = { ...tasks[index], ...updates, updatedAt: new Date().toISOString() };
    await this.saveTasks(tasks);
    return tasks[index];
  }

  async deleteTask(id: string): Promise<boolean> {
    const tasks = await this.readTasks();
    const index = tasks.findIndex(task => task.id === id);
    
    if (index === -1) {
      return false;
    }

    tasks.splice(index, 1);
    await this.saveTasks(tasks);
    return true;
  }

  async saveResult(result: ScrapingResult): Promise<void> {
    const results = await this.readResults();
    const existingIndex = results.findIndex(r => r.taskId === result.taskId);
    
    if (existingIndex >= 0) {
      results[existingIndex] = result;
    } else {
      results.push(result);
    }
    
    await this.saveResults(results);
  }

  async getResult(taskId: string): Promise<ScrapingResult | null> {
    const results = await this.readResults();
    return results.find(result => result.taskId === taskId) || null;
  }
} 