export interface ScrapingField {
  fieldName: string;
  selector: string;
  type: 'text' | 'href' | 'src' | 'attribute';
  attribute?: string; // 当type为'attribute'时使用
}

// 新增：登录配置
export interface LoginConfig {
  type: 'form' | 'cookie' | 'none';
  // 表单登录配置
  usernameSelector?: string;
  passwordSelector?: string;
  submitSelector?: string;
  username?: string;
  password?: string;
  // Cookie登录配置
  cookies?: Array<{
    name: string;
    value: string;
    domain?: string;
    path?: string;
  }>;
  // 登录前的等待时间（秒）
  waitAfterLogin?: number;
}

// 新增：页面交互配置
export interface PageInteraction {
  type: 'click' | 'scroll' | 'wait';
  selector?: string;
  x?: number;
  y?: number;
  scrollY?: number;
  waitTime?: number;
}

// 新增：智能识别结果
export interface SmartDetectionResult {
  tables: Array<{
    selector: string;
    rowSelector: string;
    columns: Array<{
      name: string;
      selector: string;
      type: 'text' | 'href' | 'src';
    }>;
    sampleData: Record<string, any>[];
  }>;
  lists: Array<{
    containerSelector: string;
    itemSelector: string;
    fields: Array<{
      name: string;
      selector: string;
      type: 'text' | 'href' | 'src';
    }>;
    sampleData: Record<string, any>[];
  }>;
}

// 新增：页面截图信息
export interface PageScreenshot {
  screenshot: string;
  title: string;
  url: string;
  viewport: {
    width: number;
    height: number;
  };
  fullPage: boolean;
  scrollHeight?: number;
}

// 新增：元素选择结果
export interface ElementSelection {
  selector: string;
  element: {
    tagName: string;
    text: string;
    attributes: Record<string, string>;
    rect: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };
  suggestedField: {
    name: string;
    type: 'text' | 'href' | 'src' | 'attribute';
    attribute?: string;
  };
}

export interface ScrapingTask {
  id: string;
  name: string;
  url: string;
  fields: ScrapingField[];
  nextPageSelector?: string;
  status: 'pending' | 'running' | 'success' | 'failed';
  createdAt: string;
  updatedAt: string;
  error?: string;
  // 新增字段
  loginConfig?: LoginConfig;
  preActions?: PageInteraction[]; // 抓取前的预操作
  smartDetection?: SmartDetectionResult; // 智能识别结果
}

export interface ScrapingResult {
  taskId: string;
  data: Record<string, any>[];
  totalCount: number;
  scrapedAt: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface CreateTaskRequest {
  name: string;
  url: string;
  fields: ScrapingField[];
  nextPageSelector?: string;
  loginConfig?: LoginConfig;
  preActions?: PageInteraction[];
}

export interface UpdateTaskRequest extends Partial<CreateTaskRequest> {
  status?: ScrapingTask['status'];
} 