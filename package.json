{"name": "puppeteer-web-scraper", "version": "1.0.0", "description": "基于Puppeteer的可视化网页抓取平台", "private": true, "scripts": {"dev": "concurrently \"pnpm run dev:backend\" \"pnpm run dev:frontend\"", "dev:frontend": "cd frontend && pnpm run dev", "dev:backend": "cd backend && pnpm run dev", "build": "pnpm run build:frontend && pnpm run build:backend", "build:frontend": "cd frontend && pnpm run build", "build:backend": "cd backend && pnpm run build", "install:all": "pnpm install && cd frontend && pnpm install && cd ../backend && pnpm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=20.0.0"}, "volta": {"node": "20.18.3"}}