export interface ScrapingField {
  fieldName: string;
  selector: string;
  type: 'text' | 'href' | 'src' | 'attribute';
  attribute?: string;
}

// 新增：登录配置
export interface LoginConfig {
  type: 'form' | 'cookie' | 'none';
  // 表单登录配置
  usernameSelector?: string;
  passwordSelector?: string;
  submitSelector?: string;
  username?: string;
  password?: string;
  // Cookie登录配置
  cookies?: Array<{
    name: string;
    value: string;
    domain?: string;
    path?: string;
  }>;
  // 登录前的等待时间（秒）
  waitAfterLogin?: number;
}

// 新增：页面交互配置
export interface PageInteraction {
  type: 'click' | 'scroll' | 'wait';
  selector?: string;
  x?: number;
  y?: number;
  scrollY?: number;
  waitTime?: number;
}

// 新增：智能识别结果
export interface SmartDetectionResult {
  tables: Array<{
    selector: string;
    rowSelector: string;
    columns: Array<{
      name: string;
      selector: string;
      type: 'text' | 'href' | 'src';
    }>;
    sampleData: Record<string, any>[];
  }>;
  lists: Array<{
    containerSelector: string;
    itemSelector: string;
    fields: Array<{
      name: string;
      selector: string;
      type: 'text' | 'href' | 'src';
    }>;
    sampleData: Record<string, any>[];
  }>;
}

// 更新页面预览接口
export interface PagePreview {
  screenshot: string;
  title: string;
  url: string;
  viewport: {
    width: number;
    height: number;
  };
  fullPage: boolean;
  scrollHeight?: number;
}

// 新增：元素选择结果
export interface ElementSelection {
  selector: string;
  element: {
    tagName: string;
    text: string;
    attributes: Record<string, string>;
    rect: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  };
  suggestedField: {
    name: string;
    type: 'text' | 'href' | 'src' | 'attribute';
    attribute?: string;
  };
}

export interface TaskData {
  id: string;
  name: string;
  url: string;
  fields: ScrapingField[];
  nextPageSelector?: string;
  status: 'pending' | 'running' | 'success' | 'failed';
  createdAt: string;
  updatedAt: string;
  error?: string;
  // 新增字段
  loginConfig?: LoginConfig;
  preActions?: PageInteraction[];
  smartDetection?: SmartDetectionResult;
}

export interface TaskFormData {
  name: string;
  url: string;
  fields: ScrapingField[];
  nextPageSelector?: string;
  loginConfig?: LoginConfig;
  preActions?: PageInteraction[];
}

export interface ScrapingResult {
  taskId: string;
  data: Record<string, any>[];
  totalCount: number;
  scrapedAt: string;
}

export interface SelectorValidation {
  valid: boolean;
  count: number;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export type TaskStatus = 'pending' | 'running' | 'success' | 'failed';
export type FieldType = 'text' | 'href' | 'src' | 'attribute'; 