<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>抓取任务</h2>
        <p class="page-description">管理和执行您的网页数据抓取任务</p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          :icon="Plus"
          @click="createTask"
          class="primary-btn"
          size="large"
        >
          新建任务
        </el-button>
        <el-button
          :icon="Refresh"
          @click="refreshTasks"
          class="refresh-btn"
          :loading="loading"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计信息卡片 -->
    <div class="stats-grid" v-if="tasks.length > 0">
      <div class="stat-card">
        <div class="stat-icon success">
          <el-icon><SuccessFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ successTasks }}</div>
          <div class="stat-label">成功任务</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon running">
          <el-icon><Loading /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ runningTasks }}</div>
          <div class="stat-label">运行中</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><DataBoard /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ tasks.length }}</div>
          <div class="stat-label">总任务数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon failed">
          <el-icon><WarningFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ failedTasks }}</div>
          <div class="stat-label">失败任务</div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <transition-group name="list" tag="div" class="task-grid" v-if="tasks.length > 0">
        <div
          v-for="task in tasks"
          :key="task.id"
          class="task-card-container"
        >
          <div class="task-card modern-card">
            <!-- 任务状态指示器 -->
            <div class="status-indicator" :class="task.status"></div>
            
            <div class="card-header">
              <div class="task-title-section">
                <h3 class="task-name">{{ task.name }}</h3>
                <el-tag
                  :type="getStatusType(task.status)"
                  size="small"
                  class="status-tag"
                  effect="light"
                >
                  <el-icon class="status-icon">
                    <Loading v-if="task.status === 'running'" />
                    <SuccessFilled v-else-if="task.status === 'success'" />
                    <WarningFilled v-else-if="task.status === 'failed'" />
                    <Clock v-else />
                  </el-icon>
                  {{ getStatusText(task.status) }}
                </el-tag>
              </div>
              
              <el-dropdown trigger="click" @command="(cmd: string) => handleAction(cmd, task)">
                <el-button size="small" :icon="MoreFilled" circle class="more-btn" />
                <template #dropdown>
                  <el-dropdown-menu class="action-menu">
                    <el-dropdown-item command="edit">
                      <el-icon><Edit /></el-icon>
                      <span>编辑任务</span>
                    </el-dropdown-item>
                    <el-dropdown-item command="duplicate" divided>
                      <el-icon><CopyDocument /></el-icon>
                      <span>复制任务</span>
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>
                      <span>删除任务</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <div class="card-content">
              <div class="task-info">
                <div class="info-item">
                  <el-icon class="info-icon"><Link /></el-icon>
                  <div class="info-content">
                    <span class="info-label">目标网址</span>
                    <span class="info-value">{{ task.url }}</span>
                  </div>
                </div>
                
                <div class="info-item">
                  <el-icon class="info-icon"><Collection /></el-icon>
                  <div class="info-content">
                    <span class="info-label">抓取字段</span>
                    <span class="info-value">{{ task.fields.length }} 个字段</span>
                  </div>
                </div>
                
                <div class="info-item">
                  <el-icon class="info-icon"><Clock /></el-icon>
                  <div class="info-content">
                    <span class="info-label">创建时间</span>
                    <span class="info-value">{{ formatTime(task.createdAt) }}</span>
                  </div>
                </div>
              </div>

              <!-- 字段预览 -->
              <div class="fields-preview" v-if="task.fields.length > 0">
                <div class="preview-label">字段预览</div>
                <div class="field-tags">
                  <el-tag
                    v-for="(field, index) in task.fields.slice(0, 3)"
                    :key="index"
                    size="small"
                    effect="plain"
                    class="field-tag"
                  >
                    {{ field.fieldName }}
                  </el-tag>
                  <el-tag
                    v-if="task.fields.length > 3"
                    size="small"
                    type="info"
                    effect="plain"
                    class="field-tag more-tag"
                  >
                    +{{ task.fields.length - 3 }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 错误信息 -->
            <div v-if="task.error" class="error-section">
              <el-alert
                :title="task.error"
                type="error"
                effect="light"
                show-icon
                :closable="false"
                class="error-alert"
              />
            </div>

            <div class="card-actions">
              <el-button
                type="primary"
                :icon="VideoPlay"
                :loading="task.status === 'running'"
                :disabled="task.status === 'running'"
                @click="runTask(task.id)"
                class="action-btn primary"
              >
                {{ task.status === 'running' ? '运行中...' : '执行抓取' }}
              </el-button>

              <el-button
                :icon="View"
                @click="viewResult(task.id)"
                :disabled="task.status !== 'success'"
                class="action-btn secondary"
              >
                查看结果
              </el-button>
            </div>
          </div>
        </div>
      </transition-group>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-content">
          <div class="empty-icon">
            <el-icon><FolderOpened /></el-icon>
          </div>
          <h3 class="empty-title">暂无抓取任务</h3>
          <p class="empty-description">
            创建您的第一个任务来开始抓取网页数据
          </p>
          <el-button
            type="primary"
            :icon="Plus"
            @click="createTask"
            size="large"
            class="empty-action"
          >
            创建第一个任务
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Link,
  Collection,
  Clock,
  VideoPlay,
  View,
  MoreFilled,
  Edit,
  CopyDocument,
  Delete,
  SuccessFilled,
  WarningFilled,
  Loading,
  DataBoard,
  FolderOpened
} from '@element-plus/icons-vue'
import { taskApi } from '@/api'
import type { TaskData as ScrapingTask, TaskStatus } from '@/types'
import dayjs from 'dayjs'

const router = useRouter()
const tasks = ref<ScrapingTask[]>([])
const loading = ref(false)

// 计算任务统计
const successTasks = computed(() => tasks.value.filter((t: ScrapingTask) => t.status === 'success').length)
const runningTasks = computed(() => tasks.value.filter((t: ScrapingTask) => t.status === 'running').length)
const failedTasks = computed(() => tasks.value.filter((t: ScrapingTask) => t.status === 'failed').length)

// 获取任务列表
const fetchTasks = async () => {
  try {
    loading.value = true
    const response = await taskApi.getTasks()
    if (response.success && response.data) {
      tasks.value = response.data
    }
  } catch (error) {
    ElMessage.error('获取任务列表失败')
    console.error('获取任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理下拉菜单操作
const handleAction = (command: string, task: ScrapingTask) => {
  switch (command) {
    case 'edit':
      editTask(task.id)
      break
    case 'duplicate':
      duplicateTask(task)
      break
    case 'delete':
      deleteTask(task.id)
      break
  }
}

// 创建任务
const createTask = () => {
  router.push('/tasks/create')
}

// 编辑任务
const editTask = (id: string) => {
  router.push(`/tasks/${id}/edit`)
}

// 查看结果
const viewResult = (id: string) => {
  router.push(`/tasks/${id}/result`)
}

// 执行任务
const runTask = async (id: string) => {
  try {
    const response = await taskApi.runTask(id)
    if (response.success) {
      ElMessage.success('抓取任务已开始执行')
      // 更新任务状态
      const taskIndex = tasks.value.findIndex((t: ScrapingTask) => t.id === id)
      if (taskIndex >= 0) {
        tasks.value[taskIndex].status = 'running'
      }
      // 定期检查任务状态
      checkTaskStatus(id)
    }
  } catch (error) {
    ElMessage.error('启动抓取任务失败')
    console.error('启动抓取任务失败:', error)
  }
}

// 检查任务状态
const checkTaskStatus = async (id: string) => {
  const maxAttempts = 30 // 最多检查30次
  let attempts = 0
  
  const checkStatus = async () => {
    try {
      attempts++
      const response = await taskApi.getTask(id)
      if (response.success && response.data) {
        const task = response.data
        const taskIndex = tasks.value.findIndex((t: ScrapingTask) => t.id === id)
        if (taskIndex >= 0) {
          tasks.value[taskIndex] = task
        }
        
        // 如果任务完成或失败，停止检查
        if (task.status === 'success' || task.status === 'failed') {
          if (task.status === 'success') {
            ElMessage.success('抓取任务执行成功')
          } else {
            ElMessage.error(`抓取任务执行失败: ${task.error || '未知错误'}`)
          }
          return
        }
        
        // 如果任务仍在运行且未超过最大尝试次数，继续检查
        if (task.status === 'running' && attempts < maxAttempts) {
          setTimeout(checkStatus, 2000) // 2秒后再次检查
        }
      }
    } catch (error) {
      console.error('检查任务状态失败:', error)
    }
  }
  
  setTimeout(checkStatus, 2000) // 2秒后开始检查
}

// 复制任务
const duplicateTask = async (task: ScrapingTask) => {
  try {
    const newTask = {
      name: `${task.name} (副本)`,
      url: task.url,
      fields: task.fields,
      nextPageSelector: task.nextPageSelector
    }
    
    const response = await taskApi.createTask(newTask)
    if (response.success) {
      ElMessage.success('任务复制成功')
      await fetchTasks()
    }
  } catch (error) {
    ElMessage.error('复制任务失败')
    console.error('复制任务失败:', error)
  }
}

// 删除任务
const deleteTask = async (id: string) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个抓取任务吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await taskApi.deleteTask(id)
    if (response.success) {
      ElMessage.success('任务删除成功')
      await fetchTasks()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
      console.error('删除任务失败:', error)
    }
  }
}

// 刷新任务列表
const refreshTasks = () => {
  fetchTasks()
}

// 获取状态类型
const getStatusType = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, string> = {
    pending: '',
    running: 'info',
    success: 'success',
    failed: 'danger'
  }
  return statusMap[status]
}

// 获取状态文本
const getStatusText = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, string> = {
    pending: '待执行',
    running: '运行中',
    success: '成功',
    failed: '失败'
  }
  return statusMap[status]
}

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  fetchTasks()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 0 4px;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 16px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.primary-btn {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  font-weight: 600;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
}

.primary-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.refresh-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: var(--color-bg-primary);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  padding: 24px;
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-icon.success {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: var(--color-success);
}

.stat-icon.running {
  background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
  color: var(--color-info);
}

.stat-icon.total {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: var(--color-primary);
}

.stat-icon.failed {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: var(--color-danger);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* 任务列表样式 */
.task-list {
  min-height: 400px;
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.task-card-container {
  position: relative;
}

.task-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--color-primary-light);
}

/* 状态指示器 */
.status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.status-indicator.pending {
  background: linear-gradient(180deg, var(--color-warning) 0%, #f59e0b 100%);
}

.status-indicator.running {
  background: linear-gradient(180deg, var(--color-info) 0%, #0ea5e9 100%);
}

.status-indicator.success {
  background: linear-gradient(180deg, var(--color-success) 0%, #059669 100%);
}

.status-indicator.failed {
  background: linear-gradient(180deg, var(--color-danger) 0%, #dc2626 100%);
}

.card-header {
  padding: 20px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.task-title-section {
  flex: 1;
  margin-right: 12px;
}

.task-name {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
  line-height: 1.3;
  word-break: break-word;
}

.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: var(--radius-lg);
}

.status-tag :deep(.el-tag__content) {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-icon {
  font-size: 14px;
}

.more-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.more-btn:hover {
  background: var(--color-bg-primary);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
}

.card-content {
  padding: 20px;
  flex: 1;
}

.task-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  color: var(--color-text-muted);
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 14px;
  color: var(--color-text-primary);
  word-break: break-all;
  line-height: 1.4;
}

.fields-preview {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border-light);
}

.preview-label {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.field-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.field-tag {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  font-size: 12px;
  padding: 4px 8px;
  border-radius: var(--radius-md);
}

.more-tag {
  background: var(--color-bg-tertiary);
  color: var(--color-text-muted);
}

.error-section {
  padding: 0 20px 20px 20px;
}

.error-alert {
  border-radius: var(--radius-md);
}

.card-actions {
  padding: 20px;
  border-top: 1px solid var(--color-border-light);
  display: flex;
  gap: 12px;
  background: var(--color-bg-secondary);
}

.action-btn {
  flex: 1;
  font-weight: 500;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
}

.action-btn.secondary:hover {
  background: var(--color-bg-secondary);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* 下拉菜单样式 */
.action-menu {
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-border-light);
}

.action-menu .el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-weight: 500;
}

.danger-item {
  color: var(--color-danger) !important;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 80px;
  color: var(--color-text-muted);
  margin-bottom: 24px;
}

.empty-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.empty-description {
  margin: 0 0 32px 0;
  color: var(--color-text-secondary);
  font-size: 16px;
  line-height: 1.5;
}

.empty-action {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

/* 动画效果 */
.list-enter-active, .list-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.list-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.list-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.list-move {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .task-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .task-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .header-actions .el-button {
    width: 100%;
  }
  
  .task-card {
    margin: 0 -4px;
  }
  
  .card-header {
    padding: 16px 16px 0 16px;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .card-actions {
    padding: 16px;
  }
}
</style>