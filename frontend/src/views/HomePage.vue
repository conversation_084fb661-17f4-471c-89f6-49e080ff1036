<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <span class="gradient-text">智能网页抓取</span>
            <br>
            让数据收集变得简单
          </h1>
          <p class="hero-subtitle">
            无需编程经验，通过可视化界面轻松配置网页抓取任务。
            <br>
            一键抓取、实时预览、数据导出，释放数据价值。
          </p>
          <div class="hero-actions">
            <el-button 
              type="primary" 
              size="large" 
              class="cta-button"
              @click="$router.push('/tasks/create')"
            >
              <el-icon><Plus /></el-icon>
              立即开始
            </el-button>
            <el-button 
              size="large" 
              class="secondary-button"
              @click="$router.push('/tasks')"
            >
              查看任务
            </el-button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="floating-card demo-card">
            <div class="card-header">
              <div class="card-controls">
                <span class="control red"></span>
                <span class="control yellow"></span>
                <span class="control green"></span>
              </div>
              <span class="card-title">抓取配置</span>
            </div>
            <div class="card-content">
              <div class="demo-field">
                <span class="field-label">标题</span>
                <span class="field-selector">.post-title</span>
              </div>
              <div class="demo-field">
                <span class="field-label">链接</span>
                <span class="field-selector">.post-link</span>
              </div>
              <div class="demo-field">
                <span class="field-label">时间</span>
                <span class="field-selector">.post-date</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特性 -->
    <section class="features-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">核心功能</h2>
          <p class="section-subtitle">简单易用的可视化网页抓取解决方案</p>
        </div>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.id">
            <div class="feature-icon">
              <component :is="feature.icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作流程 -->
    <section class="workflow-section">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">简单三步，轻松抓取</h2>
          <p class="section-subtitle">无需复杂配置，几分钟完成数据抓取</p>
        </div>
        <div class="workflow-steps">
          <div 
            class="step-card" 
            v-for="(step, index) in workflowSteps" 
            :key="step.id"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
            <div v-if="index < workflowSteps.length - 1" class="step-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 开始行动 -->
    <section class="cta-section">
      <div class="cta-container">
        <div class="cta-content">
          <h2 class="cta-title">准备好开始了吗？</h2>
          <p class="cta-subtitle">创建您的第一个抓取任务，体验数据收集的便捷</p>
          <div class="cta-actions">
            <el-button 
              type="primary" 
              size="large" 
              class="cta-button"
              @click="$router.push('/tasks/create')"
            >
              创建抓取任务
            </el-button>
          </div>
        </div>
        <div class="cta-stats">
          <div class="stat-item">
            <div class="stat-number">简单</div>
            <div class="stat-label">可视化配置</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">快速</div>
            <div class="stat-label">一键抓取</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">高效</div>
            <div class="stat-label">数据导出</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { Plus, ArrowRight, Monitor, Setting, Download, Cpu } from '@element-plus/icons-vue'

const features = [
  {
    id: 1,
    icon: Monitor,
    title: '可视化选择',
    description: '在真实网页中点击要抓取的元素，无需编写复杂的选择器代码'
  },
  {
    id: 2,
    icon: Setting,
    title: '智能配置',
    description: '自动识别元素类型，支持文本、链接、图片等多种数据类型抓取'
  },
  {
    id: 3,
    icon: Cpu,
    title: '批量处理',
    description: '支持分页抓取，一次配置可处理大量页面数据'
  },
  {
    id: 4,
    icon: Download,
    title: '数据导出',
    description: '支持CSV、JSON等多种格式导出，方便后续数据分析'
  }
]

const workflowSteps = [
  {
    id: 1,
    title: '输入网址',
    description: '输入要抓取的目标网页URL'
  },
  {
    id: 2,
    title: '选择元素',
    description: '在预览页面中点击要抓取的内容'
  },
  {
    id: 3,
    title: '导出数据',
    description: '运行任务并导出结构化数据'
  }
]
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-bg-primary) 0%, var(--color-bg-secondary) 100%);
}

/* 英雄区域 */
.hero-section {
  padding: 80px 32px 120px;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  min-height: calc(100vh - 160px);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  width: 100%;
}

.hero-text {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.hero-title {
  font-size: 52px;
  font-weight: 700;
  line-height: 1.1;
  margin: 0;
  color: var(--color-text-primary);
}

.gradient-text {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 20px;
  line-height: 1.6;
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: 400;
}

.hero-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.cta-button {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cta-button:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.secondary-button {
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 500;
  border-radius: var(--radius-lg);
  background: var(--color-bg-primary);
  border: 2px solid var(--color-border);
  color: var(--color-text-primary);
  transition: all 0.3s ease;
}

.secondary-button:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 演示卡片 */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.demo-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--color-border-light);
  overflow: hidden;
  width: 320px;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-light);
}

.card-controls {
  display: flex;
  gap: 6px;
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ff5f57; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #28ca42; }

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-secondary);
}

.card-content {
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.demo-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border-light);
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.field-selector {
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  color: var(--color-primary);
  background: rgba(79, 70, 229, 0.1);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

/* 功能特性区域 */
.features-section {
  padding: 120px 32px;
  background: var(--color-bg-secondary);
}

.section-container {
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 40px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: var(--color-text-primary);
}

.section-subtitle {
  font-size: 18px;
  color: var(--color-text-secondary);
  margin: 0;
  font-weight: 400;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
}

.feature-card {
  background: var(--color-bg-primary);
  border-radius: var(--radius-xl);
  padding: 40px 32px;
  text-align: center;
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary-light);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  font-size: 24px;
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: var(--color-text-primary);
}

.feature-description {
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-text-secondary);
  margin: 0;
}

/* 工作流程区域 */
.workflow-section {
  padding: 120px 32px;
  background: var(--color-bg-primary);
}

.workflow-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.step-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  box-shadow: var(--shadow-md);
}

.step-content {
  max-width: 200px;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--color-text-primary);
}

.step-description {
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text-secondary);
  margin: 0;
}

.step-arrow {
  position: absolute;
  right: -32px;
  top: 25px;
  color: var(--color-text-muted);
  font-size: 20px;
  z-index: 1;
}

/* CTA区域 */
.cta-section {
  padding: 120px 32px;
  background: linear-gradient(135deg, var(--color-bg-secondary) 0%, var(--color-bg-tertiary) 100%);
}

.cta-container {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 60px;
}

.cta-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.cta-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0;
  color: var(--color-text-primary);
}

.cta-subtitle {
  font-size: 18px;
  color: var(--color-text-secondary);
  margin: 0;
  max-width: 600px;
}

.cta-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 600px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
  
  .hero-title {
    font-size: 44px;
  }
  
  .workflow-steps {
    flex-direction: column;
    gap: 40px;
  }
  
  .step-arrow {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 60px 20px 80px;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .hero-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .cta-button, .secondary-button {
    width: 100%;
    padding: 16px 24px;
  }
  
  .features-section, .workflow-section, .cta-section {
    padding: 80px 20px;
  }
  
  .section-title {
    font-size: 32px;
  }
  
  .cta-title {
    font-size: 28px;
  }
  
  .cta-stats {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 480px) {
  .demo-card {
    width: 280px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-card {
    padding: 32px 24px;
  }
}
</style>