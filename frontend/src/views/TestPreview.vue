<template>
  <div class="test-preview">
    <h2>预览功能测试</h2>
    
    <div class="test-form">
      <el-input 
        v-model="testUrl" 
        placeholder="输入要测试的URL"
        style="width: 400px; margin-right: 10px;"
      />
      <el-button 
        @click="testPreview" 
        :loading="loading"
        type="primary"
      >
        测试预览
      </el-button>
    </div>
    
    <div v-if="loading" class="loading-info">
      <p>正在加载预览...</p>
      <el-progress :percentage="progress" />
    </div>
    
    <div v-if="error" class="error-info">
      <el-alert type="error" :title="error" show-icon />
    </div>
    
    <div v-if="result" class="result-info">
      <h3>预览结果:</h3>
      <p>标题: {{ result.title }}</p>
      <p>URL: {{ result.url }}</p>
      <p>视口: {{ result.viewport.width }}x{{ result.viewport.height }}</p>
      <p>滚动高度: {{ result.scrollHeight }}</p>
      <div class="screenshot">
        <img :src="result.screenshot" alt="页面截图" style="max-width: 100%; border: 1px solid #ccc;" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const testUrl = ref('https://www.baidu.com')
const loading = ref(false)
const progress = ref(0)
const error = ref('')
const result = ref<any>(null)

const testPreview = async () => {
  if (!testUrl.value) {
    ElMessage.warning('请输入URL')
    return
  }
  
  loading.value = true
  progress.value = 0
  error.value = ''
  result.value = null
  
  try {
    progress.value = 20
    
    console.log('发送预览请求:', testUrl.value)
    
    const response = await axios.post('/api/scraper/preview', {
      url: testUrl.value,
      fullPage: true
    })
    
    progress.value = 80
    console.log('收到响应:', response.data)
    
    if (response.data.success) {
      result.value = response.data.data
      progress.value = 100
      ElMessage.success('预览加载成功')
    } else {
      throw new Error(response.data.error || '预览失败')
    }
  } catch (err: any) {
    console.error('预览失败:', err)
    error.value = err.response?.data?.error || err.message || '未知错误'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
    progress.value = 0
  }
}
</script>

<style scoped>
.test-preview {
  padding: 20px;
}

.test-form {
  margin: 20px 0;
}

.loading-info, .error-info, .result-info {
  margin: 20px 0;
}

.screenshot {
  margin-top: 10px;
}
</style>
