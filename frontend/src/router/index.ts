import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/HomePage.vue'),
      meta: {
        title: '首页'
      }
    },
    {
      path: '/tasks',
      name: 'TaskList',
      component: () => import('@/views/TaskList.vue'),
      meta: {
        title: '抓取任务列表'
      }
    },
    {
      path: '/tasks/create',
      name: 'TaskCreate',
      component: () => import('@/views/TaskConfig.vue'),
      meta: {
        title: '创建抓取任务'
      }
    },
    {
      path: '/tasks/:id/edit',
      name: 'TaskEdit',
      component: () => import('@/views/TaskConfig.vue'),
      meta: {
        title: '编辑抓取任务'
      }
    },
    {
      path: '/tasks/:id/result',
      name: 'TaskResult',
      component: () => import('@/views/TaskResult.vue'),
      meta: {
        title: '抓取结果'
      }
    },
    {
      path: '/test-preview',
      name: 'TestPreview',
      component: () => import('@/views/TestPreview.vue'),
      meta: {
        title: '预览功能测试'
      }
    }
  ]
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Puppeteer 抓取平台`
  }
  next()
})

export default router 