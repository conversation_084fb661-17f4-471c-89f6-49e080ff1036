import axios, { AxiosResponse } from 'axios'
import type { 
  ApiResponse, 
  TaskData, 
  TaskFormData, 
  ScrapingResult, 
  PagePreview, 
  SelectorValidation,
  SmartDetectionResult,
  ElementSelection,
  LoginConfig,
  PageInteraction
} from '@/types'

const api = axios.create({
  baseURL: (import.meta as any).env?.DEV ? '/api' : 'http://localhost:3001/api',
  timeout: 60000 // 增加超时时间以支持长时间操作
})

// 任务相关API
export const taskApi = {
  // 获取任务列表
  getTasks: (): Promise<ApiResponse<TaskData[]>> => 
    api.get('/tasks').then((res: AxiosResponse<ApiResponse<TaskData[]>>) => res.data),

  // 获取单个任务
  getTask: (id: string): Promise<ApiResponse<TaskData>> => 
    api.get(`/tasks/${id}`).then((res: AxiosResponse<ApiResponse<TaskData>>) => res.data),

  // 创建任务
  createTask: (data: TaskFormData): Promise<ApiResponse<TaskData>> => 
    api.post('/tasks', data).then((res: AxiosResponse<ApiResponse<TaskData>>) => res.data),

  // 更新任务
  updateTask: (id: string, data: Partial<TaskFormData>): Promise<ApiResponse<TaskData>> => 
    api.put(`/tasks/${id}`, data).then((res: AxiosResponse<ApiResponse<TaskData>>) => res.data),

  // 删除任务
  deleteTask: (id: string): Promise<ApiResponse> => 
    api.delete(`/tasks/${id}`).then((res: AxiosResponse<ApiResponse>) => res.data),

  // 执行任务
  runTask: (id: string): Promise<ApiResponse<ScrapingResult>> => 
    api.post(`/tasks/${id}/run`).then((res: AxiosResponse<ApiResponse<ScrapingResult>>) => res.data),

  // 获取任务结果
  getTaskResult: (id: string): Promise<ApiResponse<ScrapingResult>> => 
    api.get(`/tasks/${id}/result`).then((res: AxiosResponse<ApiResponse<ScrapingResult>>) => res.data),

  // 导出结果
  exportResult: (id: string, format: 'csv' | 'json'): Promise<Blob> => 
    api.get(`/tasks/${id}/export?format=${format}`, { responseType: 'blob' }).then(res => res.data),
}

// 增强的抓取相关API
export const scraperApi = {
  // 增强的页面预览（支持登录和全屏）
  getPagePreview: (url: string, fullPage: boolean = true, loginConfig?: LoginConfig): Promise<ApiResponse<PagePreview>> => 
    api.post('/scraper/preview', { url, fullPage, loginConfig }).then((res: AxiosResponse<ApiResponse<PagePreview>>) => res.data),

  // 智能检测页面结构
  smartDetectPageStructure: (url: string, loginConfig?: LoginConfig): Promise<ApiResponse<SmartDetectionResult>> => 
    api.post('/scraper/smart-detect', { url, loginConfig }).then((res: AxiosResponse<ApiResponse<SmartDetectionResult>>) => res.data),

  // 根据坐标获取元素信息
  getElementAtCoordinate: (url: string, x: number, y: number, loginConfig?: LoginConfig): Promise<ApiResponse<ElementSelection>> => 
    api.post('/scraper/element-at-coordinate', { url, x, y, loginConfig }).then((res: AxiosResponse<ApiResponse<ElementSelection>>) => res.data),

  // 执行页面交互
  performPageInteraction: (url: string, interactions: PageInteraction[], loginConfig?: LoginConfig): Promise<ApiResponse<PagePreview>> => 
    api.post('/scraper/interact', { url, interactions, loginConfig }).then((res: AxiosResponse<ApiResponse<PagePreview>>) => res.data),

  // 验证选择器（保持向后兼容）
  validateSelector: (url: string, selector: string): Promise<ApiResponse<SelectorValidation>> => 
    api.post('/scraper/validate-selector', { url, selector }).then((res: AxiosResponse<ApiResponse<SelectorValidation>>) => res.data),

  // 注入选择器脚本（保持向后兼容）
  injectSelectorScript: (url: string): Promise<ApiResponse<{ html: string }>> => 
    api.post('/scraper/inject-selector', { url }).then((res: AxiosResponse<ApiResponse<{ html: string }>>) => res.data),
}

export default api 