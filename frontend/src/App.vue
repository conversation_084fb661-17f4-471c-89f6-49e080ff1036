<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 顶部导航栏 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="header-left">
            <div class="app-logo">
              <el-icon class="logo-icon">
                <Operation />
              </el-icon>
              <div class="logo-text">
                <h1 class="app-title">Puppeteer 抓取平台</h1>
                <span class="app-subtitle">可视化网页数据抓取工具</span>
              </div>
            </div>
            
            <!-- 面包屑导航 -->
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item to="/">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentPageTitle && currentPageTitle !== '首页'">{{ currentPageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <!-- 快捷操作 -->
            <div class="quick-actions">
              <el-tooltip content="刷新页面" placement="bottom">
                <el-button
                  circle
                  :icon="Refresh"
                  @click="refreshPage"
                  class="action-btn"
                />
              </el-tooltip>
              
              <el-tooltip content="新建任务" placement="bottom">
                <el-button
                  type="primary"
                  :icon="Plus"
                  @click="$router.push('/tasks/create')"
                  class="primary-action-btn"
                >
                  新建任务
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
        
        <!-- 进度指示器 -->
        <div class="progress-bar" v-if="loading">
          <div class="progress-fill"></div>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="app-main">
        <div class="main-content">
          <!-- 页面状态指示器 -->
          <div v-if="loading">
            <transition name="fade" mode="out-in">
              <div class="page-loading">
                <div class="loading-content">
                  <div class="loading-spinner"></div>
                  <p>加载中...</p>
                </div>
              </div>
            </transition>
          </div>
          <router-view v-else v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
        
        <!-- 浮动操作按钮 -->
        <transition name="fade">
          <div class="fab-container" v-if="showFab">
            <el-tooltip content="回到顶部" placement="left">
              <el-button
                circle
                type="primary"
                :icon="Top"
                @click="scrollToTop"
                class="fab-btn"
                size="large"
              />
            </el-tooltip>
          </div>
        </transition>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { Plus, Operation, Refresh, Top } from '@element-plus/icons-vue'

const route = useRoute()
const loading = ref(false)
const showFab = ref(false)

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title as string || ''
})

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 回到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 监听滚动事件，显示/隐藏回到顶部按钮
const handleScroll = () => {
  showFab.value = window.scrollY > 300
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-bg-secondary) 0%, var(--color-bg-primary) 100%);
}

/* 顶部导航栏样式 */
.app-header {
  background: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  padding: 0;
  height: 80px;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 32px;
  max-width: 1400px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 32px;
  flex: 1;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.app-logo:hover {
  transform: scale(1.02);
}

.logo-icon {
  font-size: 40px;
  color: var(--color-primary);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.app-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: var(--color-text-primary);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.app-subtitle {
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 500;
  opacity: 0.8;
}

.breadcrumb {
  font-size: 14px;
}

.breadcrumb :deep(.el-breadcrumb__item) {
  color: var(--color-text-secondary);
  font-weight: 500;
}

.breadcrumb :deep(.el-breadcrumb__item:last-child) {
  color: var(--color-primary);
}

.header-right {
  display: flex;
  align-items: center;
}

.quick-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-btn {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--color-bg-primary);
  border-color: var(--color-primary-light);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.primary-action-btn {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.primary-action-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 进度条 */
.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--color-bg-tertiary);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }
  50% {
    width: 75%;
    margin-left: 25%;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

/* 主内容区域 */
.app-main {
  padding: 0;
  background: transparent;
  min-height: calc(100vh - 80px);
  position: relative;
}

.main-content {
  width: 100%;
  min-height: 100%;
}

/* 页面加载状态 */
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loading-content p {
  margin: 0;
  color: var(--color-text-secondary);
  font-size: 16px;
  font-weight: 500;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 1000;
}

.fab-btn {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border: none;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-xl);
}

.fab-btn .el-icon {
  font-size: 20px;
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .header-content {
    padding: 0 24px;
  }
  
  .header-left {
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .app-header {
    height: 70px;
  }
  
  .header-content {
    padding: 0 16px;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .app-logo {
    gap: 12px;
  }
  
  .logo-icon {
    font-size: 32px;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .app-subtitle {
    font-size: 11px;
  }
  
  .breadcrumb {
    font-size: 12px;
  }
  
  .quick-actions {
    gap: 12px;
  }
  
  .primary-action-btn {
    font-size: 14px;
    padding: 8px 16px;
  }
  
  .fab-container {
    bottom: 24px;
    right: 24px;
  }
  
  .fab-btn {
    width: 48px;
    height: 48px;
  }
  
  .app-main {
    min-height: calc(100vh - 70px);
  }
}

@media (max-width: 480px) {
  .header-left {
    min-height: 60px;
  }
  
  .app-logo {
    flex-direction: row;
    align-items: center;
  }
  
  .logo-text {
    flex-direction: column;
  }
  
  .breadcrumb {
    margin-top: 4px;
  }
  
  .quick-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .primary-action-btn {
    width: 100%;
    min-width: 120px;
  }
}
</style> 