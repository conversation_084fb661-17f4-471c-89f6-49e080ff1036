{"name": "puppeteer-web-scraper-frontend", "version": "1.0.0", "description": "Puppeteer可视化抓取平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "file-saver": "^2.0.5", "pinia": "^2.1.7", "vue": "^3.4.3", "vue-router": "^4.2.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.5", "typescript": "^5.3.3", "vite": "^5.0.10", "vue-tsc": "^2.2.12"}, "engines": {"node": ">=20.0.0"}}